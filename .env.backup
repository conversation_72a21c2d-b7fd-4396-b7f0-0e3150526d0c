# LLM Configuration
# Choose your preferred provider by uncommenting the relevant section

# OpenAI Configuration (Default)
OPENAI_API_KEY=sk-t8zcWN8dFJxaD18REKRrdLzlngOJlmpkzvfomfyLwaYMNcO6
OPENAI_MODEL=gpt-4o-mini
# Custom OpenAI-compatible API (for proxies/alternatives)
OPENAI_BASE_URL=https://yuanplus.cloud/v1
# Examples for alternative providers:
# OPENAI_BASE_URL=https://yuanplus.cloud/v1
# OPENAI_BASE_URL=https://api.deepseek.com/v1
# OPENAI_BASE_URL=https://api.moonshot.cn/v1

# Anthropic Configuration
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# ANTHROPIC_MODEL=claude-3-5-sonnet-20241022

# Google Configuration
# GOOGLE_API_KEY=your_google_api_key_here
# GOOGLE_MODEL=gemini-1.5-flash

# LLM Settings
LLM_TEMPERATURE=0
LLM_MAX_TOKENS=-1

# 是否连接用户自己的浏览器
# true: 连接现有浏览器（无自动化痕迹）
# false: 使用Playwright启动新浏览器（标准模式）
BROWSER_CONNECT_TO_USER_BROWSER=true

# 连接模式：smart（智能）、connect（连接）、persistent（持久）、launch（启动）
BROWSER_CONNECTION_MODE=smart

# 调试端口配置
BROWSER_DEBUG_PORT=9222
BROWSER_DEBUG_HOST=localhost

# Edge浏览器路径
BROWSER_EXECUTABLE_PATH=C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe

# 用户数据目录
BROWSER_USER_DATA_DIR=C:\Users\<USER>\AppData\Local\Microsoft\Edge\User Data

# 反检测模式，默认关闭，按需启用
BROWSER_STEALTH_MODE=false

# 自动关闭浏览器，连接用户浏览器时建议设为false
BROWSER_AUTO_CLOSE=false

# 窗口显示配置
BROWSER_HEADLESS=false
BROWSER_MAXIMIZED=true
BROWSER_FULLSCREEN=false
BROWSER_WIDTH=1920
BROWSER_HEIGHT=1080

# GPU加速
BROWSER_ENABLE_GPU=true

# 自定义浏览器启动参数，例如：--enable-gpu,--start-maximized
BROWSER_ARGS=--enable-gpu,--start-maximized

# 页面超时配置
BROWSER_TIMEOUT=30000
BROWSER_PAGE_LOAD_TIMEOUT=30000
BROWSER_NAVIGATION_TIMEOUT=30000

# Playwright Browser Installation
BROWSER_AUTO_INSTALL=true

# Agent Configuration
AGENT_MAX_STEPS=50
AGENT_MAX_ACTIONS_PER_STEP=3
AGENT_USE_VISION=true

# Logging
LOG_LEVEL=info
DEBUG=false
