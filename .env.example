# Sentra Auto Browser 环境配置示例
# 复制此文件为 .env 并填入你的实际配置

# ===========================================
# 🧠 AI模型配置 (至少配置一个)
# ===========================================

# OpenAI 配置 (推荐)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o
OPENAI_BASE_URL=https://api.openai.com/v1

# 国内代理示例 (可选)
# OPENAI_BASE_URL=https://yuanplus.cloud/v1
# OPENAI_BASE_URL=https://api.deepseek.com/v1
# OPENAI_BASE_URL=https://api.moonshot.cn/v1

# Google Gemini 配置
# GOOGLE_API_KEY=your_google_api_key_here
# GOOGLE_MODEL=gemini-2.5-flash

# Anthropic Claude 配置
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# ANTHROPIC_MODEL=claude-sonnet-4

# ===========================================
# ⚙️ AI模型通用配置
# ===========================================

# 温度参数 (0-1, 0表示更确定性的输出)
LLM_TEMPERATURE=0

# 最大Token数量
# - 正数：设置具体的token限制（如4000、8000等）
# - -1：不限制token数量（让模型自由输出）
LLM_MAX_TOKENS=4000

# 请求超时时间 (毫秒)
LLM_TIMEOUT=30000

# ===========================================
# 🌐 浏览器基础配置
# ===========================================

# 是否使用无头模式 (true/false)
BROWSER_HEADLESS=false

# 浏览器视窗大小
BROWSER_VIEWPORT_WIDTH=1280
BROWSER_VIEWPORT_HEIGHT=720
# 兼容性别名
BROWSER_WIDTH=1280
BROWSER_HEIGHT=720

# 浏览器超时时间 (毫秒)
BROWSER_TIMEOUT=30000

# 用户数据目录 (可选，用于保持登录状态)
BROWSER_USER_DATA_DIR=./user-data

# 浏览器可执行文件路径 (可选)
# BROWSER_EXECUTABLE_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe
# BROWSER_EXECUTABLE_PATH=C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe

# 自动安装浏览器 (true/false)
BROWSER_AUTO_INSTALL=true

# 浏览器语言设置
BROWSER_LOCALE=zh-CN

# 时区设置
BROWSER_TIMEZONE=Asia/Shanghai

# 颜色主题 (light/dark/no-preference)
BROWSER_COLOR_SCHEME=light

# 浏览器启动参数 (逗号分隔)
# BROWSER_ARGS=--no-sandbox,--disable-dev-shm-usage

# ===========================================
# 🔗 CDP连接配置 (高级功能)
# ===========================================

# 是否连接到现有浏览器 (true/false)
BROWSER_CONNECT_TO_USER_BROWSER=false

# 连接模式 (smart/direct)
BROWSER_CONNECTION_MODE=smart

# 调试主机地址
BROWSER_DEBUG_HOST=localhost

# 调试端口
BROWSER_DEBUG_PORT=9222

# 是否自动关闭浏览器 (true/false)
BROWSER_AUTO_CLOSE=true

# 反检测模式 (true/false)
BROWSER_STEALTH_MODE=true

# 最大化窗口 (true/false)
BROWSER_MAXIMIZED=false

# 全屏模式 (true/false)
BROWSER_FULLSCREEN=false

# 启用GPU加速 (true/false)
BROWSER_ENABLE_GPU=true

# Kiosk模式 (true/false)
BROWSER_KIOSK_MODE=false

# ===========================================
# 🤖 代理配置
# ===========================================

# 最大执行步数
AGENT_MAX_STEPS=50

# 每步最大操作数
AGENT_MAX_ACTIONS_PER_STEP=3

# 是否使用视觉功能 (true/false)
AGENT_USE_VISION=true

# 是否重试失败的操作 (true/false)
AGENT_RETRY_FAILED_ACTIONS=true

# 最大重试次数
AGENT_MAX_RETRIES=2

# 重试延迟 (毫秒)
AGENT_RETRY_DELAY=500

# ===========================================
# 🧠 高级功能配置
# ===========================================

# 启用记忆功能 (true/false)
AGENT_ENABLE_MEMORY=true

# 记忆容量
AGENT_MEMORY_SIZE=1000

# 启用规划功能 (true/false)
AGENT_ENABLE_PLANNING=true

# 规划步数
AGENT_PLANNING_STEPS=10

# 启用反思功能 (true/false)
AGENT_ENABLE_REFLECTION=true

# 反思间隔
AGENT_REFLECTION_INTERVAL=5

# 启用错误恢复 (true/false)
AGENT_ENABLE_ERROR_RECOVERY=true

# 启用性能监控 (true/false)
AGENT_ENABLE_PERFORMANCE_MONITORING=true

# 错误时截图 (true/false)
AGENT_ENABLE_SCREENSHOT_ON_ERROR=true

# 启用操作验证 (true/false)
AGENT_ENABLE_ACTION_VALIDATION=true

# ===========================================
# 📝 日志配置
# ===========================================

# 日志级别 (debug/info/warn/error)
LOG_LEVEL=info

# 调试模式 (true/false)
DEBUG=false

# 日志文件路径 (可选)
# LOG_FILE=./logs/sentra-auto.log

# ===========================================
# 🌐 网络配置 (可选)
# ===========================================

# HTTP代理设置
# HTTP_PROXY=http://proxy:8080
# HTTPS_PROXY=https://proxy:8080

# 代理认证
# PROXY_USERNAME=username
# PROXY_PASSWORD=password

# 浏览器代理服务器
# BROWSER_PROXY_SERVER=http://proxy:8080
# BROWSER_PROXY_USERNAME=username
# BROWSER_PROXY_PASSWORD=password

# ===========================================
# ⚡ 性能优化配置
# ===========================================

# DOM缓存大小
DOM_CACHE_SIZE=1000

# 截图质量 (0-100)
SCREENSHOT_QUALITY=80

# 截图格式 (png/jpeg)
SCREENSHOT_FORMAT=png

# 并发限制
MAX_CONCURRENT_OPERATIONS=3

# 浏览器慢动作延迟 (毫秒)
BROWSER_SLOW_MO=0

# 开发者工具 (true/false)
BROWSER_DEVTOOLS=false

# 忽略HTTPS错误 (true/false)
BROWSER_IGNORE_HTTPS_ERRORS=false

# ===========================================
# 🔒 安全配置
# ===========================================

# 允许的域名列表 (逗号分隔)
# ALLOWED_DOMAINS=example.com,test.com

# 禁止的域名列表 (逗号分隔)
# BLOCKED_DOMAINS=malicious.com,spam.com

# 启用安全模式 (true/false)
SECURITY_MODE=false

# ===========================================
# 🛠️ 开发配置
# ===========================================

# 开发模式 (true/false)
DEV_MODE=false

# 保存执行历史 (true/false)
SAVE_EXECUTION_HISTORY=true

# 历史文件路径
EXECUTION_HISTORY_PATH=./history

# 启用详细错误信息 (true/false)
VERBOSE_ERRORS=true
