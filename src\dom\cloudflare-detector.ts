import { Page } from 'playwright';
import { logger } from '../utils/logger';

/**
 * 🛡️ Cloudflare挑战检测器
 * 专门用于检测和处理Cloudflare安全挑战
 */
export class CloudflareDetector {
  private page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  /**
   * 🔍 检测页面是否包含Cloudflare挑战
   */
  async detectCloudflareChallenge(): Promise<{
    hasChallenge: boolean;
    challengeType: string;
    elements: Array<{
      type: string;
      selector: string;
      description: string;
      isVisible: boolean;
      boundingBox: any;
    }>;
  }> {
    logger.info('🛡️ 开始检测Cloudflare挑战...', 'CloudflareDetector');

    const result = await this.page.evaluate(() => {
      const challenges = [];
      let hasChallenge = false;
      let challengeType = 'none';

      // 1. 检测Cloudflare挑战iframe
      const cfIframes = document.querySelectorAll('iframe[title*="Cloudflare"], iframe[title*="security challenge"], iframe[src*="challenges.cloudflare.com"]');
      cfIframes.forEach((iframe, index) => {
        const rect = iframe.getBoundingClientRect();
        const isVisible = rect.width > 0 && rect.height > 0 && 
                         window.getComputedStyle(iframe).display !== 'none' &&
                         window.getComputedStyle(iframe).visibility !== 'hidden';
        
        challenges.push({
          type: 'cloudflare-iframe',
          selector: `iframe[title="${iframe.getAttribute('title')}"]`,
          description: `Cloudflare挑战iframe: ${iframe.getAttribute('title')}`,
          isVisible,
          boundingBox: isVisible ? {
            x: rect.x,
            y: rect.y,
            width: rect.width,
            height: rect.height
          } : null
        });

        if (isVisible) {
          hasChallenge = true;
          challengeType = 'iframe-challenge';
        }
      });

      // 2. 检测Cloudflare复选框（可能在iframe内或外）
      const cfCheckboxes = document.querySelectorAll(
        'input[type="checkbox"][name*="cf"], ' +
        'input[type="checkbox"].cf-turnstile, ' +
        '.cf-turnstile input[type="checkbox"], ' +
        '.ctp-checkbox-label input, ' +
        'input[data-cf-challenge], ' +
        '[id*="cf-stage"] input[type="checkbox"]'
      );
      
      cfCheckboxes.forEach((checkbox, index) => {
        const rect = checkbox.getBoundingClientRect();
        const isVisible = rect.width > 0 && rect.height > 0 && 
                         window.getComputedStyle(checkbox).display !== 'none' &&
                         window.getComputedStyle(checkbox).visibility !== 'hidden';
        
        challenges.push({
          type: 'cloudflare-checkbox',
          selector: `input[type="checkbox"]:nth-of-type(${index + 1})`,
          description: `Cloudflare验证复选框`,
          isVisible,
          boundingBox: isVisible ? {
            x: rect.x,
            y: rect.y,
            width: rect.width,
            height: rect.height
          } : null
        });

        if (isVisible) {
          hasChallenge = true;
          challengeType = 'checkbox-challenge';
        }
      });

      // 3. 检测其他Cloudflare元素
      const cfElements = document.querySelectorAll(
        '.cf-challenge, .cf-turnstile, .cf-wrapper, ' +
        '[data-cf], [class*="cloudflare"], [id*="cloudflare"], ' +
        '.challenge-form, .challenge-stage'
      );

      cfElements.forEach((element, index) => {
        const rect = element.getBoundingClientRect();
        const isVisible = rect.width > 0 && rect.height > 0 && 
                         window.getComputedStyle(element).display !== 'none' &&
                         window.getComputedStyle(element).visibility !== 'hidden';
        
        challenges.push({
          type: 'cloudflare-element',
          selector: `.${element.className.split(' ')[0]}`,
          description: `Cloudflare挑战元素: ${element.tagName}`,
          isVisible,
          boundingBox: isVisible ? {
            x: rect.x,
            y: rect.y,
            width: rect.width,
            height: rect.height
          } : null
        });

        if (isVisible) {
          hasChallenge = true;
          if (!challengeType || challengeType === 'none') {
            challengeType = 'element-challenge';
          }
        }
      });

      // 4. 检测页面标题和内容中的Cloudflare标识
      const title = document.title.toLowerCase();
      const bodyText = document.body.innerText.toLowerCase();
      
      if (title.includes('cloudflare') || title.includes('security check') ||
          bodyText.includes('cloudflare') || bodyText.includes('checking your browser')) {
        hasChallenge = true;
        if (!challengeType || challengeType === 'none') {
          challengeType = 'page-challenge';
        }
      }

      return {
        hasChallenge,
        challengeType,
        elements: challenges
      };
    });

    if (result.hasChallenge) {
      logger.info(`🛡️ 检测到Cloudflare挑战: ${result.challengeType}`, 'CloudflareDetector');
      logger.info(`📊 发现 ${result.elements.length} 个相关元素`, 'CloudflareDetector');
    } else {
      logger.info('✅ 未检测到Cloudflare挑战', 'CloudflareDetector');
    }

    return result;
  }

  /**
   * 🎯 尝试解决Cloudflare挑战
   */
  async attemptChallengeResolution(): Promise<boolean> {
    const challenge = await this.detectCloudflareChallenge();
    
    if (!challenge.hasChallenge) {
      return true; // 没有挑战，认为已解决
    }

    logger.info('🎯 尝试解决Cloudflare挑战...', 'CloudflareDetector');

    // 查找可见的复选框
    const visibleCheckboxes = challenge.elements.filter(
      el => el.type === 'cloudflare-checkbox' && el.isVisible
    );

    if (visibleCheckboxes.length > 0) {
      try {
        const checkbox = visibleCheckboxes[0];
        logger.info(`🎯 尝试点击Cloudflare复选框: ${checkbox.description}`, 'CloudflareDetector');
        
        await this.page.click(checkbox.selector, { timeout: 5000 });
        await this.page.waitForTimeout(2000); // 等待挑战处理
        
        // 重新检测是否还有挑战
        const newChallenge = await this.detectCloudflareChallenge();
        return !newChallenge.hasChallenge;
        
      } catch (error: any) {
        logger.error(`❌ 点击Cloudflare复选框失败: ${error.message}`, error, 'CloudflareDetector');
        return false;
      }
    }

    // 查找可见的iframe
    const visibleIframes = challenge.elements.filter(
      el => el.type === 'cloudflare-iframe' && el.isVisible
    );

    if (visibleIframes.length > 0) {
      try {
        const iframe = visibleIframes[0];
        logger.info(`🎯 尝试与Cloudflare iframe交互: ${iframe.description}`, 'CloudflareDetector');
        
        // 尝试点击iframe区域
        if (iframe.boundingBox) {
          const centerX = iframe.boundingBox.x + iframe.boundingBox.width / 2;
          const centerY = iframe.boundingBox.y + iframe.boundingBox.height / 2;
          
          await this.page.click(`body`, { 
            position: { x: centerX, y: centerY },
            timeout: 5000 
          });
          await this.page.waitForTimeout(3000); // 等待挑战处理
          
          // 重新检测是否还有挑战
          const newChallenge = await this.detectCloudflareChallenge();
          return !newChallenge.hasChallenge;
        }
        
      } catch (error: any) {
        logger.error(`❌ 与Cloudflare iframe交互失败: ${error.message}`, error, 'CloudflareDetector');
        return false;
      }
    }

    logger.warn('⚠️ 无法自动解决Cloudflare挑战', 'CloudflareDetector');
    return false;
  }

  /**
   * 🕐 等待Cloudflare挑战完成
   */
  async waitForChallengeCompletion(timeout: number = 30000): Promise<boolean> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      const challenge = await this.detectCloudflareChallenge();
      
      if (!challenge.hasChallenge) {
        logger.info('✅ Cloudflare挑战已完成', 'CloudflareDetector');
        return true;
      }
      
      await this.page.waitForTimeout(1000);
    }
    
    logger.warn('⏰ 等待Cloudflare挑战完成超时', 'CloudflareDetector');
    return false;
  }
}
