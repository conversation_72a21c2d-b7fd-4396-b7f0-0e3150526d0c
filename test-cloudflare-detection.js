/**
 * 🛡️ Cloudflare挑战检测测试脚本
 * 用于测试DOM元素检测对CF挑战的改进效果
 */

const { BrowserSession } = require('./dist/browser/session');
const { logger } = require('./dist/utils/logger');

async function testCloudflareDetection() {
  console.log('🛡️ 开始测试Cloudflare挑战检测...\n');

  const browserSession = new BrowserSession({
    headless: false,
    userDataDir: './test-user-data',
    debugPort: 9222
  });

  try {
    // 启动浏览器
    console.log('🚀 启动浏览器...');
    await browserSession.start();
    
    // 启用增强模式
    console.log('⚡ 启用增强模式...');
    await browserSession.enableEnhancedMode();

    // 测试网站列表（已知有CF保护的网站）
    const testSites = [
      'https://nowsecure.nl',
      'https://bot.sannysoft.com',
      'https://httpbin.org/headers'
    ];

    for (const site of testSites) {
      console.log(`\n🌐 测试网站: ${site}`);
      
      try {
        // 导航到测试网站
        await browserSession.navigate(site);
        
        // 等待页面加载
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 获取DOM状态
        console.log('🔍 分析DOM元素...');
        const domState = await browserSession.getEnhancedDOMState(true);
        
        console.log(`📊 检测到 ${domState.elements.length} 个交互元素`);
        
        // 查找可能的CF挑战元素
        const cfElements = domState.elements.filter(el => 
          el.tagName === 'iframe' ||
          (el.attributes && (
            el.attributes.title?.toLowerCase().includes('cloudflare') ||
            el.attributes.title?.toLowerCase().includes('security challenge') ||
            el.attributes.src?.includes('challenges.cloudflare.com') ||
            el.attributes.class?.includes('cf-') ||
            el.attributes.class?.includes('captcha')
          )) ||
          el.text?.toLowerCase().includes('cloudflare') ||
          el.text?.toLowerCase().includes('验证') ||
          el.text?.toLowerCase().includes('challenge')
        );
        
        if (cfElements.length > 0) {
          console.log(`🛡️ 发现 ${cfElements.length} 个可能的CF挑战元素:`);
          cfElements.forEach((el, index) => {
            console.log(`  ${index + 1}. ${el.tagName} - ${el.text || el.attributes?.title || '无文本'}`);
            if (el.attributes?.src) {
              console.log(`     src: ${el.attributes.src}`);
            }
            if (el.attributes?.title) {
              console.log(`     title: ${el.attributes.title}`);
            }
            console.log(`     可见: ${el.isVisible ? '是' : '否'}`);
            console.log(`     交互: ${el.isInteractive ? '是' : '否'}`);
          });
        } else {
          console.log('✅ 未发现CF挑战元素');
        }
        
        // 检查iframe元素
        const iframes = domState.elements.filter(el => el.tagName === 'iframe');
        if (iframes.length > 0) {
          console.log(`📦 发现 ${iframes.length} 个iframe元素:`);
          iframes.forEach((iframe, index) => {
            console.log(`  ${index + 1}. iframe`);
            console.log(`     src: ${iframe.attributes?.src || '无src'}`);
            console.log(`     title: ${iframe.attributes?.title || '无title'}`);
            console.log(`     可见: ${iframe.isVisible ? '是' : '否'}`);
            console.log(`     交互: ${iframe.isInteractive ? '是' : '否'}`);
            if (iframe.securityChallenge) {
              console.log(`     🛡️ 标记为安全挑战`);
            }
            if (iframe.crossOriginIframe) {
              console.log(`     🔒 跨域iframe`);
            }
          });
        }
        
        // 检查复选框元素
        const checkboxes = domState.elements.filter(el => 
          el.tagName === 'input' && el.attributes?.type === 'checkbox'
        );
        if (checkboxes.length > 0) {
          console.log(`☑️ 发现 ${checkboxes.length} 个复选框元素:`);
          checkboxes.forEach((checkbox, index) => {
            console.log(`  ${index + 1}. checkbox`);
            console.log(`     name: ${checkbox.attributes?.name || '无name'}`);
            console.log(`     class: ${checkbox.attributes?.class || '无class'}`);
            console.log(`     可见: ${checkbox.isVisible ? '是' : '否'}`);
            console.log(`     交互: ${checkbox.isInteractive ? '是' : '否'}`);
          });
        }
        
      } catch (error) {
        console.error(`❌ 测试网站 ${site} 失败:`, error.message);
      }
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    // 清理
    try {
      await browserSession.close();
      console.log('\n✅ 浏览器已关闭');
    } catch (error) {
      console.error('❌ 关闭浏览器失败:', error);
    }
  }
}

// 运行测试
if (require.main === module) {
  testCloudflareDetection().catch(console.error);
}

module.exports = { testCloudflareDetection };
